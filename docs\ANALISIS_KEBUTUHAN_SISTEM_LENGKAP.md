# ANALISIS KEBUTUHAN SISTEM RENTAL GENSET

## 📋 DAFTAR ISI

1. [<PERSON><PERSON><PERSON>](#analisis-kebutuhan-sistem)
2. [Analis<PERSON>](#analisis-kebutuhan-data)
3. [Ana<PERSON><PERSON> PIECES](#analisis-pieces)
4. [Analisis <PERSON>an Data & Informasi](#analisis-kebutuhan-data--informasi)

---

## 🎯 ANALISIS KEBUTUHAN SISTEM

### 1. Ke<PERSON><PERSON>an Fungsional

#### 1.1 Manajemen Autentikasi & Pengguna

**Kebutuhan:**

- Sistem registrasi dan login dengan email/password
- Autentikasi berbasis role (USER/ADMIN)
- Manajemen profil pengguna
- Session management yang aman
- Integrasi Google OAuth (opsional)

**Implementasi Teknologi:**

- Better Auth untuk authentication
- PostgreSQL untuk penyimpanan user data
- Role-based access control (RBAC)

#### 1.2 Manajemen Produk Genset

**Kebutuhan:**

- CRUD operations untuk produk genset
- Manajemen spesifikasi (nama, kapasitas, tipe)
- Pengaturan harga per hari dan overtime rate
- Upload dan manajemen gambar produk
- Tracking status ketersediaan (AVAILABLE/MAINTENANCE/NOT_AVAILABLE)
- Manajemen stok inventory

**Fitur Utama:**

- Katalog produk dengan filter dan pencarian
- Detail produk dengan spesifikasi lengkap
- Status real-time ketersediaan

#### 1.3 Sistem Pemesanan (Rental)

**Kebutuhan:**

- Form pemesanan dengan validasi data
- Kalkulasi biaya otomatis
- Validasi ketersediaan produk
- Manajemen status rental (PENDING → CONFIRMED → ACTIVE → COMPLETED)
- Pencatatan waktu operasional
- Perhitungan overtime otomatis

**Workflow Rental:**

1. User memilih produk dan mengisi form
2. Sistem validasi ketersediaan dan kalkulasi biaya
3. Pembuatan record rental dengan status PENDING
4. Admin konfirmasi rental (status → CONFIRMED)
5. Operasional dimulai (status → ACTIVE)
6. Selesai operasional (status → COMPLETED)

#### 1.4 Sistem Pembayaran

**Kebutuhan:**

- Integrasi payment gateway (Midtrans)
- Sistem deposit 50% untuk user baru
- Pembayaran bertahap (deposit → remaining payment)
- Perhitungan overtime cost
- Payment status tracking

**Payment Flow:**

- Deposit payment (50% untuk user baru)
- Remaining payment setelah operasional selesai
- Overtime payment jika ada

#### 1.5 Manajemen Operasional

**Kebutuhan:**

- Tracking operational start/end time
- Perhitungan durasi aktual
- Monitoring overtime hours
- Status operasional real-time

#### 1.6 Reporting & Analytics

**Kebutuhan:**

- Dashboard admin dengan KPI
- Laporan rental harian/bulanan
- Analisis pendapatan
- Customer analytics
- Operational reports

### 2. Kebutuhan Non-Fungsional

#### 2.1 Performance

- Response time < 2 detik untuk operasi CRUD
- Support concurrent users (minimal 50 users)
- Database optimization dengan indexing

#### 2.2 Security

- Enkripsi password dengan bcrypt
- HTTPS untuk semua komunikasi
- CSRF protection
- Rate limiting untuk API
- Input validation dan sanitization

#### 2.3 Usability

- Mobile-first responsive design
- Intuitive user interface
- Bahasa Indonesia untuk semua interface
- Touch-friendly design (44px minimum touch targets)

#### 2.4 Reliability

- 99% uptime availability
- Data backup dan recovery
- Error handling yang robust
- Transaction rollback untuk operasi critical

#### 2.5 Scalability

- Horizontal scaling capability
- Database connection pooling
- Caching strategy untuk performance

### 3. Kebutuhan Teknologi

#### 3.1 Frontend

- **Framework:** Next.js 14 dengan App Router
- **UI Library:** React dengan shadcn/ui components
- **Styling:** Tailwind CSS
- **State Management:** React hooks dan server actions
- **Animation:** GSAP untuk payment success/failure states

#### 3.2 Backend

- **Runtime:** Node.js
- **Framework:** Next.js API Routes
- **ORM:** Prisma
- **Authentication:** Better Auth
- **File Upload:** Next.js built-in capabilities

#### 3.3 Database

- **Primary Database:** PostgreSQL
- **Connection:** Prisma Client
- **Migration:** Prisma Migrate
- **Backup:** Automated database backup

#### 3.4 External Services

- **Payment Gateway:** Midtrans
- **File Storage:** Local storage atau cloud storage

#### 3.5 Development Tools

- **Version Control:** Git
- **Package Manager:** npm/yarn/pnpm
- **Code Quality:** ESLint, Prettier
- **Testing:** Jest, React Testing Library (recommended)

---

## 📊 ANALISIS KEBUTUHAN DATA

### 1. Entitas Data Utama

#### 1.1 User (Pengguna)

```sql
User {
  id: String (PK, CUID)
  name: String?
  email: String (UNIQUE)
  password: String?
  phone: String?
  role: UserRole (USER/ADMIN)
  image: String?
  createdAt: DateTime
  updatedAt: DateTime
}
```

**Atribut Kunci:**

- `email`: Unique identifier untuk login
- `role`: Menentukan level akses (USER/ADMIN)
- `phone`: Untuk kontak customer

#### 1.2 Product (Genset)

```sql
Product {
  id: String (PK, CUID)
  name: String
  price: Float (harga per hari)
  description: String?
  image: String?
  capacity: Int (kapasitas dalam VA/KVA)
  stock: Int
  status: ProductStatus (AVAILABLE/NOT_AVAILABLE/MAINTENANCE)
  category: String?
  overtimeRate: Float? (tarif lembur per jam)
  userId: String (FK to User)
  createdAt: DateTime
  updatedAt: DateTime
}
```

**Atribut Kunci:**

- `capacity`: Spesifikasi teknis genset
- `overtimeRate`: Tarif lembur yang berbeda per produk
- `status`: Status ketersediaan real-time

#### 1.3 Rental (Transaksi Rental)

```sql
Rental {
  id: String (PK, CUID)
  userId: String (FK to User)
  productId: String (FK to Product)
  startDate: DateTime
  endDate: DateTime
  arrivalTime: String
  purpose: String
  amount: Float
  address: String?
  duration: String?
  notes: String?
  status: RentalStatus (PENDING/CONFIRMED/ACTIVE/COMPLETED/CANCELLED)
  operationalStart: DateTime?
  operationalEnd: DateTime?
  overtimeHours: Float?
  createdAt: DateTime
  updatedAt: DateTime
}
```

**Atribut Kunci:**

- `operationalStart/End`: Waktu operasional aktual
- `overtimeHours`: Jam lembur yang dihitung otomatis
- `status`: Tracking progress rental

#### 1.4 Payment (Pembayaran)

```sql
Payment {
  id: String (PK, CUID)
  rentalId: String (FK to Rental, UNIQUE)
  amount: Float (total amount)
  deposit: Float (50% untuk user baru)
  remaining: Float (sisa pembayaran)
  overtimeCost: Float? (biaya lembur)
  transactionId: String? (Midtrans transaction ID)
  status: PaymentStatus (DEPOSIT_PENDING/DEPOSIT_PAID/FULLY_PAID/FAILED/INVOICE_ISSUED)
  createdAt: DateTime
  updatedAt: DateTime
}
```

**Atribut Kunci:**

- `deposit/remaining`: Split payment system
- `overtimeCost`: Biaya tambahan untuk overtime
- `transactionId`: Integrasi dengan Midtrans

#### 1.5 PaymentStatusHistory (Riwayat Status Pembayaran)

```sql
PaymentStatusHistory {
  id: String (PK, CUID)
  paymentId: String (FK to Payment)
  userId: String (FK to User)
  newStatus: PaymentStatus
  createdAt: DateTime
}
```

### 2. Relasi Data

#### 2.1 Relasi Utama

- **User → Rental**: One-to-Many (satu user bisa punya banyak rental)
- **Product → Rental**: One-to-Many (satu produk bisa dirental berkali-kali)
- **Rental → Payment**: One-to-One (setiap rental punya satu payment record)
- **Payment → PaymentStatusHistory**: One-to-Many (tracking perubahan status)

#### 2.2 Constraint dan Index

- **Unique Constraints**: User.email, Payment.rentalId
- **Indexes**: Product.status, Rental.status, Payment.rentalId
- **Foreign Key Constraints**: Semua relasi dengan CASCADE delete

### 3. Data Flow

#### 3.1 Rental Creation Flow

```
User Input → Validation → Product Check → Price Calculation →
Rental Creation → Payment Record → Midtrans Integration
```

#### 3.2 Payment Flow

```
Payment Request → Midtrans Token → User Payment →
Webhook Callback → Status Update
```

#### 3.3 Operational Flow

```
Rental Confirmed → Operational Start → Time Tracking →
Operational End → Overtime Calculation → Final Payment
```

---

## 🔍 ANALISIS PIECES

### P - Performance (Kinerja)

#### Masalah Saat Ini:

- **Manual Processing**: Proses rental masih manual dengan pencatatan kertas
- **Slow Response**: Konfirmasi rental membutuhkan waktu lama
- **Calculation Errors**: Perhitungan biaya manual rawan kesalahan
- **No Real-time Tracking**: Tidak ada monitoring real-time status rental

#### Solusi yang Diusulkan:

- **Automated Processing**: Sistem otomatis untuk pemesanan dan kalkulasi
- **Real-time Updates**: Status tracking real-time untuk semua stakeholder
- **Auto Calculation**: Perhitungan biaya otomatis termasuk overtime
- **Performance Optimization**: Database indexing dan caching

#### Manfaat:

- Waktu proses rental berkurang dari 30 menit menjadi 5 menit
- Eliminasi kesalahan perhitungan manual
- Real-time visibility untuk admin dan customer

### I - Information (Informasi)

#### Masalah Saat Ini:

- **Data Scattered**: Informasi tersebar di berbagai tempat
- **No Historical Data**: Tidak ada riwayat rental yang terstruktur
- **Limited Reporting**: Laporan manual dan tidak real-time
- **No Analytics**: Tidak ada analisis performa bisnis

#### Solusi yang Diusulkan:

- **Centralized Database**: Semua data terpusat di PostgreSQL
- **Comprehensive Reporting**: Dashboard dengan KPI dan analytics
- **Historical Tracking**: Riwayat lengkap semua transaksi
- **Real-time Information**: Data real-time untuk decision making

#### Manfaat:

- Akses informasi real-time 24/7
- Historical data untuk analisis trend
- Automated reporting untuk management

### E - Economics (Ekonomi)

#### Masalah Saat Ini:

- **Manual Labor Cost**: Biaya tenaga kerja untuk proses manual tinggi
- **Paper Cost**: Biaya kertas dan administrasi manual
- **Error Cost**: Kerugian akibat kesalahan perhitungan
- **Opportunity Cost**: Kehilangan peluang bisnis karena proses lambat

#### Solusi yang Diusulkan:

- **Process Automation**: Otomatisasi mengurangi biaya operasional
- **Digital Documentation**: Eliminasi biaya kertas dan printing
- **Error Reduction**: Sistem otomatis mengurangi kesalahan
- **Faster Processing**: Peningkatan throughput bisnis

#### Analisis Cost-Benefit:

- **Development Cost**: Rp 50,000,000 (one-time)
- **Operational Savings**: Rp 10,000,000/bulan
- **ROI**: 5 bulan payback period
- **Long-term Benefits**: Scalability untuk growth bisnis

### C - Control (Kontrol)

#### Masalah Saat Ini:

- **No Access Control**: Tidak ada pembatasan akses data
- **Manual Authorization**: Proses approval manual dan lambat
- **No Audit Trail**: Tidak ada jejak perubahan data
- **Security Risks**: Data tidak terproteksi dengan baik

#### Solusi yang Diusulkan:

- **Role-based Access**: Kontrol akses berdasarkan role (USER/ADMIN)
- **Automated Workflow**: Workflow approval otomatis
- **Audit Logging**: Tracking semua perubahan data
- **Security Implementation**: Enkripsi dan authentication

#### Manfaat:

- Keamanan data terjamin
- Accountability yang jelas
- Compliance dengan standar keamanan

### E - Efficiency (Efisiensi)

#### Masalah Saat Ini:

- **Redundant Data Entry**: Input data berulang di berbagai form
- **Manual Calculations**: Perhitungan manual memakan waktu
- **Paper-based Process**: Proses berbasis kertas tidak efisien
- **Communication Delays**: Komunikasi manual menyebabkan delay

#### Solusi yang Diusulkan:

- **Single Data Entry**: Input sekali, digunakan di seluruh sistem
- **Automated Calculations**: Kalkulasi otomatis real-time
- **Digital Workflow**: Proses digital end-to-end
- **Real-time Updates**: Update status real-time

#### Manfaat:

- Efisiensi waktu 80% lebih cepat
- Eliminasi duplikasi data
- Update status yang akurat

### S - Service (Layanan)

#### Masalah Saat Ini:

- **Limited Availability**: Layanan hanya tersedia jam kerja
- **Manual Customer Service**: Respon customer service lambat
- **No Self-service**: Customer tidak bisa cek status sendiri
- **Inconsistent Service**: Kualitas layanan tidak konsisten

#### Solusi yang Diusulkan:

- **24/7 Availability**: Sistem tersedia 24 jam
- **Self-service Portal**: Customer bisa cek status mandiri
- **Real-time Updates**: Update status real-time ke customer
- **Consistent Experience**: Standardisasi proses layanan

#### Manfaat:

- Customer satisfaction meningkat
- Availability 24/7 untuk booking
- Consistent service quality

---

## 📈 ANALISIS KEBUTUHAN DATA & INFORMASI

### 1. Kebutuhan Data Input

#### 1.1 Data Master

- **User Data**: Nama, email, phone, role
- **Product Data**: Spesifikasi genset, harga, overtime rate
- **Configuration Data**: System settings, payment config

#### 1.2 Data Transaksi

- **Rental Data**: Periode, lokasi, keperluan, biaya
- **Payment Data**: Amount, status, transaction details
- **Operational Data**: Start/end time, overtime hours

#### 1.3 Data Referensi

- **Status Codes**: Rental status, payment status
- **Business Rules**: Pricing rules, overtime calculation

### 2. Kebutuhan Data Output

#### 2.1 Reports

- **Daily Rental Report**: Rental harian dengan status
- **Monthly Revenue Report**: Pendapatan bulanan
- **Product Performance**: Analisis performa produk
- **Customer Analytics**: Analisis customer behavior

#### 2.2 Documents

- **Operational Report**: Laporan operasional
- **Payment Receipt**: Bukti pembayaran

### 3. Kebutuhan Informasi Real-time

#### 3.1 Dashboard Metrics

- **Active Rentals**: Jumlah rental aktif
- **Pending Payments**: Pembayaran tertunda
- **Available Products**: Produk tersedia
- **Revenue Today**: Pendapatan hari ini

#### 3.2 Status Tracking

- **Rental Progress**: Status real-time rental
- **Payment Status**: Progress pembayaran
- **Product Availability**: Ketersediaan real-time
- **Operational Status**: Status operasional

### 4. Kebutuhan Analisis Data

#### 4.1 Business Intelligence

- **Trend Analysis**: Analisis trend rental
- **Seasonal Patterns**: Pola musiman permintaan
- **Customer Segmentation**: Segmentasi customer
- **Product Optimization**: Optimasi portfolio produk

#### 4.2 Operational Analytics

- **Utilization Rate**: Tingkat utilisasi genset
- **Overtime Analysis**: Analisis overtime patterns
- **Efficiency Metrics**: Metrik efisiensi operasional
- **Cost Analysis**: Analisis struktur biaya

### 5. Data Security & Privacy

#### 5.1 Data Protection

- **Personal Data**: Enkripsi data personal customer
- **Payment Data**: Secure handling payment information
- **Business Data**: Proteksi data bisnis sensitif

#### 5.2 Access Control

- **Role-based Access**: Akses berdasarkan role
- **Data Masking**: Masking data sensitif
- **Audit Trail**: Jejak akses dan perubahan data

#### 5.3 Compliance

- **Data Retention**: Policy penyimpanan data
- **Privacy Policy**: Kebijakan privasi data
- **GDPR Compliance**: Kepatuhan regulasi data

---

## 🎯 KESIMPULAN ANALISIS

### Prioritas Implementasi:

1. **High Priority**: Core rental system, payment integration
2. **Medium Priority**: Reporting, analytics dashboard
3. **Low Priority**: Advanced features, optimization

### Success Metrics:

- **Efficiency**: 80% reduction in processing time
- **Accuracy**: 99% accuracy in calculations
- **Availability**: 99% system uptime
- **User Satisfaction**: 90% customer satisfaction rate

### Risk Mitigation:

- **Technical Risk**: Comprehensive testing, backup systems
- **Business Risk**: Phased implementation, user training
- **Security Risk**: Security audit, penetration testing

Sistem rental genset ini dirancang untuk memberikan solusi komprehensif yang efisien, aman, dan scalable untuk mendukung pertumbuhan bisnis rental genset.
